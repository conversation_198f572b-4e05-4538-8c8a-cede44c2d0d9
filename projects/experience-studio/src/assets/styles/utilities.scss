/*------------------------------------*\
  #UTILITY CLASSES
  Generic, reusable utility classes for common patterns
\*------------------------------------*/

/*------------------------------------*\
  #LAYOUT UTILITIES
\*------------------------------------*/

// Flexbox utilities
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: center;
}

.self-end {
  align-self: flex-end;
}

// Position utilities
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.static {
  position: static;
}

// Display utilities
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

// Overflow utilities
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-visible {
  overflow: visible;
}

// Width utilities
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: fit-content;
}

// Height utilities
.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

.h-fit {
  height: fit-content;
}

/*------------------------------------*\
  #SPACING UTILITIES
\*------------------------------------*/

// Margin utilities
.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }
.m-10 { margin: 2.5rem; }
.m-12 { margin: 3rem; }
.m-16 { margin: 4rem; }
.m-auto { margin: auto; }

// Margin top
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-10 { margin-top: 2.5rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }
.mt-auto { margin-top: auto; }

// Margin right
.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-5 { margin-right: 1.25rem; }
.mr-6 { margin-right: 1.5rem; }
.mr-8 { margin-right: 2rem; }
.mr-10 { margin-right: 2.5rem; }
.mr-12 { margin-right: 3rem; }
.mr-16 { margin-right: 4rem; }
.mr-auto { margin-right: auto; }

// Margin bottom
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

// Margin left
.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-5 { margin-left: 1.25rem; }
.ml-6 { margin-left: 1.5rem; }
.ml-8 { margin-left: 2rem; }
.ml-10 { margin-left: 2.5rem; }
.ml-12 { margin-left: 3rem; }
.ml-16 { margin-left: 4rem; }
.ml-auto { margin-left: auto; }

// Padding utilities
.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-10 { padding: 2.5rem; }
.p-12 { padding: 3rem; }
.p-16 { padding: 4rem; }

// Padding top
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-5 { padding-top: 1.25rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }
.pt-10 { padding-top: 2.5rem; }
.pt-12 { padding-top: 3rem; }
.pt-16 { padding-top: 4rem; }

// Padding right
.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 0.25rem; }
.pr-2 { padding-right: 0.5rem; }
.pr-3 { padding-right: 0.75rem; }
.pr-4 { padding-right: 1rem; }
.pr-5 { padding-right: 1.25rem; }
.pr-6 { padding-right: 1.5rem; }
.pr-8 { padding-right: 2rem; }
.pr-10 { padding-right: 2.5rem; }
.pr-12 { padding-right: 3rem; }
.pr-16 { padding-right: 4rem; }

// Padding bottom
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-5 { padding-bottom: 1.25rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }
.pb-10 { padding-bottom: 2.5rem; }
.pb-12 { padding-bottom: 3rem; }
.pb-16 { padding-bottom: 4rem; }

// Padding left
.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 0.25rem; }
.pl-2 { padding-left: 0.5rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }
.pl-5 { padding-left: 1.25rem; }
.pl-6 { padding-left: 1.5rem; }
.pl-8 { padding-left: 2rem; }
.pl-10 { padding-left: 2.5rem; }
.pl-12 { padding-left: 3rem; }
.pl-16 { padding-left: 4rem; }

// Gap utilities
.gap-0 { gap: 0; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-10 { gap: 2.5rem; }
.gap-12 { gap: 3rem; }
.gap-16 { gap: 4rem; }

/*------------------------------------*\
  #BORDER & RADIUS UTILITIES
\*------------------------------------*/

// Border radius
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

// Border width
.border-0 { border-width: 0; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }

/*------------------------------------*\
  #TYPOGRAPHY UTILITIES
\*------------------------------------*/

// Font weight
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

// Font size
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

// Text alignment
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// Text decoration
.underline { text-decoration: underline; }
.no-underline { text-decoration: none; }
.line-through { text-decoration: line-through; }

// Text transform
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// White space
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/*------------------------------------*\
  #OPACITY UTILITIES
\*------------------------------------*/

.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

/*------------------------------------*\
  #Z-INDEX UTILITIES
\*------------------------------------*/

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/*------------------------------------*\
  #TRANSITION UTILITIES
\*------------------------------------*/

.transition-none { transition: none; }
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }
.transition-opacity { transition: opacity 0.15s ease-in-out; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out; }
.transition-transform { transition: transform 0.15s ease-in-out; }

// Duration
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

// Timing functions
.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/*------------------------------------*\
  #TRANSFORM UTILITIES
\*------------------------------------*/

.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.transform-none { transform: none; }
.transform-gpu { transform: translate3d(var(--tw-translate-x, 0), var(--tw-translate-y, 0), 0) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

// Scale
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

// Rotate
.rotate-0 { transform: rotate(0deg); }
.rotate-1 { transform: rotate(1deg); }
.rotate-2 { transform: rotate(2deg); }
.rotate-3 { transform: rotate(3deg); }
.rotate-6 { transform: rotate(6deg); }
.rotate-12 { transform: rotate(12deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.-rotate-180 { transform: rotate(-180deg); }
.-rotate-90 { transform: rotate(-90deg); }
.-rotate-45 { transform: rotate(-45deg); }
.-rotate-12 { transform: rotate(-12deg); }
.-rotate-6 { transform: rotate(-6deg); }
.-rotate-3 { transform: rotate(-3deg); }
.-rotate-2 { transform: rotate(-2deg); }
.-rotate-1 { transform: rotate(-1deg); }

// Translate
.translate-x-0 { transform: translateX(0); }
.translate-x-1 { transform: translateX(0.25rem); }
.translate-x-2 { transform: translateX(0.5rem); }
.translate-x-3 { transform: translateX(0.75rem); }
.translate-x-4 { transform: translateX(1rem); }
.translate-x-5 { transform: translateX(1.25rem); }
.translate-x-6 { transform: translateX(1.5rem); }
.translate-x-8 { transform: translateX(2rem); }
.translate-x-10 { transform: translateX(2.5rem); }
.translate-x-12 { transform: translateX(3rem); }
.translate-x-16 { transform: translateX(4rem); }
.translate-x-20 { transform: translateX(5rem); }
.translate-x-24 { transform: translateX(6rem); }
.translate-x-32 { transform: translateX(8rem); }
.translate-x-40 { transform: translateX(10rem); }
.translate-x-48 { transform: translateX(12rem); }
.translate-x-56 { transform: translateX(14rem); }
.translate-x-64 { transform: translateX(16rem); }
.-translate-x-1 { transform: translateX(-0.25rem); }
.-translate-x-2 { transform: translateX(-0.5rem); }
.-translate-x-3 { transform: translateX(-0.75rem); }
.-translate-x-4 { transform: translateX(-1rem); }
.-translate-x-5 { transform: translateX(-1.25rem); }
.-translate-x-6 { transform: translateX(-1.5rem); }
.-translate-x-8 { transform: translateX(-2rem); }
.-translate-x-10 { transform: translateX(-2.5rem); }
.-translate-x-12 { transform: translateX(-3rem); }
.-translate-x-16 { transform: translateX(-4rem); }
.-translate-x-20 { transform: translateX(-5rem); }

.translate-y-0 { transform: translateY(0); }
.translate-y-1 { transform: translateY(0.25rem); }
.translate-y-2 { transform: translateY(0.5rem); }
.translate-y-3 { transform: translateY(0.75rem); }
.translate-y-4 { transform: translateY(1rem); }
.translate-y-5 { transform: translateY(1.25rem); }
.translate-y-6 { transform: translateY(1.5rem); }
.translate-y-8 { transform: translateY(2rem); }
.translate-y-10 { transform: translateY(2.5rem); }
.translate-y-12 { transform: translateY(3rem); }
.translate-y-16 { transform: translateY(4rem); }
.translate-y-20 { transform: translateY(5rem); }
.-translate-y-1 { transform: translateY(-0.25rem); }
.-translate-y-2 { transform: translateY(-0.5rem); }
.-translate-y-3 { transform: translateY(-0.75rem); }
.-translate-y-4 { transform: translateY(-1rem); }
.-translate-y-5 { transform: translateY(-1.25rem); }
.-translate-y-6 { transform: translateY(-1.5rem); }
.-translate-y-8 { transform: translateY(-2rem); }
.-translate-y-10 { transform: translateY(-2.5rem); }
.-translate-y-12 { transform: translateY(-3rem); }
.-translate-y-16 { transform: translateY(-4rem); }
.-translate-y-20 { transform: translateY(-5rem); }

/*------------------------------------*\
  #ANIMATION UTILITIES
\*------------------------------------*/

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

// Animation keyframes
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/*------------------------------------*\
  #THEME UTILITIES
\*------------------------------------*/

// Theme-aware text colors
.text-primary {
  .light & { color: var(--color-primary, #6b46c1); }
  .dark & { color: var(--color-primary-light, #9f7aea); }
}

.text-secondary {
  .light & { color: var(--text-secondary, #4a5568); }
  .dark & { color: var(--text-secondary-dark, #cbd5e0); }
}

.text-primary-text {
  .light & { color: var(--text-primary, #2d3748); }
  .dark & { color: var(--text-primary-dark, #f8f9fa); }
}

// Theme-aware background colors
.bg-card {
  .light & { background-color: var(--chat-window-card-bg-color); }
  .dark & { background-color: var(--chat-window-card-bg-color); }
}

.bg-primary {
  .light & { background-color: var(--color-primary, #6b46c1); }
  .dark & { background-color: var(--color-primary-light, #9f7aea); }
}

// Theme-aware borders
.border-light {
  .light & { background-color: #e9ecef; }
  .dark & { background-color: #555; }
}

/*------------------------------------*\
  #COMPONENT UTILITIES
\*------------------------------------*/

// Circle utilities
.circle {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-sm {
  width: 1rem;
  height: 1rem;
}

.circle-md {
  width: 1.5rem;
  height: 1.5rem;
}

.circle-lg {
  width: 2rem;
  height: 2rem;
}

.circle-xl {
  width: 2.5rem;
  height: 2.5rem;
}

// Button utilities
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.btn-md {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.375rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  border-radius: 0.5rem;
}

.btn-circle {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  padding: 0;
}

.btn-circle-sm {
  width: 1.5rem;
  height: 1.5rem;
}

.btn-circle-lg {
  width: 2.5rem;
  height: 2.5rem;
}

// Card utilities
.card {
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-hover {
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Backdrop utilities
.backdrop-blur {
  backdrop-filter: blur(10px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/*------------------------------------*\
  #STATE UTILITIES
\*------------------------------------*/

// Visibility states
.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

// Pointer events
.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

// Cursor utilities
.cursor-default {
  cursor: default;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-wait {
  cursor: wait;
}

.cursor-text {
  cursor: text;
}

.cursor-move {
  cursor: move;
}

.cursor-help {
  cursor: help;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// Selection utilities
.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.select-all {
  user-select: all;
}

.select-auto {
  user-select: auto;
}

/*------------------------------------*\
  #INTERACTION UTILITIES
\*------------------------------------*/

// Hover effects
.hover-lift {
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.hover-scale {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.hover-opacity {
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}

// Focus utilities
.focus-outline {
  &:focus {
    outline: 2px solid var(--color-primary, #6b46c1);
    outline-offset: 2px;
  }
}

.focus-ring {
  &:focus {
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.1);
  }
}

// Active states
.active-scale {
  &:active {
    transform: scale(0.95);
  }
}

.active-opacity {
  &:active {
    opacity: 0.7;
  }
}

/*------------------------------------*\
  #SHADOW UTILITIES
\*------------------------------------*/

.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

.shadow-2xl {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.shadow-inner {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

/*------------------------------------*\
  #GRADIENT UTILITIES
\*------------------------------------*/

.gradient-primary {
  background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
}

.gradient-purple {
  background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
}

.gradient-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
}
